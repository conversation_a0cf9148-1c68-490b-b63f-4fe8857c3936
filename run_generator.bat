@echo off
chcp 65001 >nul
echo ========================================
echo 车载场景图片问题自动生成器
echo ========================================
echo.

:input_folder
set /p folder_path="请输入图片文件夹路径: "
if not exist "%folder_path%" (
    echo 错误：文件夹不存在，请重新输入
    goto input_folder
)

echo.
echo 选择运行模式：
echo 1. 不使用AI（生成所有问题，适合手动标注）
echo 2. 使用OpenAI GPT-4V（需要API密钥）
echo 3. 使用其他大模型（需要API密钥）
echo.
set /p mode="请选择模式 (1-3): "

if "%mode%"=="1" (
    echo.
    echo 正在生成问题（不使用AI）...
    python image_question_generator.py "%folder_path%" --no-ai
    goto end
)

if "%mode%"=="2" (
    set /p api_key="请输入OpenAI API密钥: "
    echo.
    echo 正在使用OpenAI分析图片并生成问题...
    python image_question_generator.py "%folder_path%" -k "%api_key%" -m openai
    goto end
)

if "%mode%"=="3" (
    echo.
    echo 支持的模型类型：
    echo - openai: OpenAI GPT-4V
    echo - claude: Claude Vision
    echo - qwen: 通义千问VL
    echo - wenxin: 文心一言
    echo.
    set /p model_type="请输入模型类型: "
    set /p api_key="请输入API密钥: "
    echo.
    echo 正在使用 %model_type% 分析图片并生成问题...
    python image_question_generator.py "%folder_path%" -k "%api_key%" -m "%model_type%"
    goto end
)

echo 无效的选择，请重新运行脚本
goto input_folder

:end
echo.
echo 处理完成！请查看生成的Excel文件。
pause
