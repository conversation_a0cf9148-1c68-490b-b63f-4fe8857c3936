#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
车载场景图片问题生成器使用示例
"""

from image_question_generator import ImageQuestionGenerator
import os

def example_basic_usage():
    """基础使用示例（不使用AI）"""
    print("=== 基础使用示例 ===")
    
    # 创建生成器实例（不使用AI）
    generator = ImageQuestionGenerator()
    
    # 指定图片文件夹路径
    image_folder = r"D:\车载测试图片"  # 请替换为你的实际路径
    
    # 检查文件夹是否存在
    if not os.path.exists(image_folder):
        print(f"图片文件夹不存在: {image_folder}")
        print("请创建文件夹并放入一些测试图片，或修改路径")
        return
    
    try:
        # 处理图片并生成问题
        output_file = generator.process_images(
            folder_path=image_folder,
            output_file="基础问题生成结果.xlsx",
            use_ai=False  # 不使用AI
        )
        print(f"✅ 成功生成问题文件: {output_file}")
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")


def example_ai_usage():
    """使用AI的示例"""
    print("\n=== AI使用示例 ===")
    
    # 你的API密钥（请替换为实际密钥）
    api_key = "your_api_key_here"
    
    if api_key == "your_api_key_here":
        print("请先设置你的API密钥")
        return
    
    # 创建生成器实例（使用OpenAI）
    generator = ImageQuestionGenerator(api_key=api_key, model_type="openai")
    
    # 指定图片文件夹路径
    image_folder = r"D:\车载测试图片"  # 请替换为你的实际路径
    
    if not os.path.exists(image_folder):
        print(f"图片文件夹不存在: {image_folder}")
        return
    
    try:
        # 处理图片并生成问题（使用AI智能筛选）
        output_file = generator.process_images(
            folder_path=image_folder,
            output_file="AI智能问题生成结果.xlsx",
            use_ai=True  # 使用AI
        )
        print(f"✅ 成功生成问题文件: {output_file}")
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")


def example_custom_questions():
    """自定义问题示例"""
    print("\n=== 自定义问题示例 ===")
    
    generator = ImageQuestionGenerator()
    
    # 你可以修改问题模板
    custom_questions = {
        "自定义分类": {
            "子分类1": [
                "这是一个自定义问题1？",
                "这是一个自定义问题2？"
            ]
        }
    }
    
    # 添加自定义问题到现有模板
    generator.question_templates.update(custom_questions)
    
    print("已添加自定义问题模板")


def example_single_image():
    """单张图片处理示例"""
    print("\n=== 单张图片处理示例 ===")
    
    generator = ImageQuestionGenerator()
    
    # 单张图片路径
    image_path = r"D:\车载测试图片\test_image.jpg"  # 请替换为实际路径
    
    if not os.path.exists(image_path):
        print(f"图片文件不存在: {image_path}")
        return
    
    try:
        # 分析单张图片
        analysis_result = generator._basic_image_analysis(image_path)
        
        # 生成问题
        questions = generator.generate_questions_for_image(image_path, analysis_result)
        
        print(f"为图片 {os.path.basename(image_path)} 生成了 {len(questions)} 个问题")
        
        # 显示前5个问题
        print("\n前5个问题示例:")
        for i, q in enumerate(questions[:5], 1):
            print(f"{i}. [{q['分类']}-{q['子分类']}] {q['问题']}")
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")


def main():
    """主函数"""
    print("车载场景图片问题生成器 - 使用示例")
    print("=" * 50)
    
    # 运行各种示例
    example_basic_usage()
    example_ai_usage()
    example_custom_questions()
    example_single_image()
    
    print("\n" + "=" * 50)
    print("示例运行完成！")
    print("\n使用提示:")
    print("1. 修改image_folder变量为你的实际图片文件夹路径")
    print("2. 如果使用AI功能，请设置正确的API密钥")
    print("3. 确保图片文件夹中有PNG或JPG格式的图片")
    print("4. 生成的Excel文件将保存在当前目录")


if __name__ == "__main__":
    main()
