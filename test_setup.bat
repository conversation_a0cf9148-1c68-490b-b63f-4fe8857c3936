@echo off
chcp 65001 >nul
title 环境测试
cls

echo ========================================
echo 环境测试脚本
echo ========================================
echo.

echo 1. 检查Python安装...
python --version
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo 请从 https://www.python.org/downloads/ 下载并安装Python
    goto error_exit
) else (
    echo ✅ Python已安装
)
echo.

echo 2. 检查pip...
pip --version
if errorlevel 1 (
    echo ❌ pip不可用
    goto error_exit
) else (
    echo ✅ pip可用
)
echo.

echo 3. 检查当前目录文件...
if exist "image_question_generator.py" (
    echo ✅ 主程序文件存在
) else (
    echo ❌ 主程序文件不存在
    goto error_exit
)

if exist "requirements.txt" (
    echo ✅ 依赖文件存在
) else (
    echo ❌ 依赖文件不存在
    goto error_exit
)
echo.

echo 4. 安装依赖包...
pip install pandas openpyxl pillow requests
if errorlevel 1 (
    echo ❌ 依赖包安装失败
    goto error_exit
) else (
    echo ✅ 依赖包安装成功
)
echo.

echo 5. 测试导入...
python -c "import pandas; import openpyxl; import PIL; import requests; print('✅ 所有包导入成功')"
if errorlevel 1 (
    echo ❌ 包导入失败
    goto error_exit
)
echo.

echo ========================================
echo ✅ 环境检查完成，一切正常！
echo ========================================
echo.
echo 现在你可以：
echo 1. 双击 run_generator.bat 使用图形界面
echo 2. 或者在命令行运行：
echo    python image_question_generator.py "图片文件夹路径" --no-ai
echo.
pause
exit /b 0

:error_exit
echo.
echo ========================================
echo ❌ 环境检查失败
echo ========================================
echo 请解决上述问题后重新运行此脚本
echo.
pause
exit /b 1
