@echo off
title Environment Test
cls

echo ========================================
echo Environment Test Script
echo ========================================
echo.

echo 1. Checking Python installation...
python --version 2>nul
if %errorlevel% neq 0 (
    echo X Python not installed or not in PATH
    echo Please download and install Python from python.org
    goto error_exit
) else (
    echo OK Python is installed
)
echo.

echo 2. Checking pip...
pip --version 2>nul
if %errorlevel% neq 0 (
    echo X pip not available
    goto error_exit
) else (
    echo OK pip is available
)
echo.

echo 3. Checking current directory files...
if exist "image_question_generator.py" (
    echo OK Main program file exists
) else (
    echo X Main program file not found
    goto error_exit
)

if exist "requirements.txt" (
    echo OK Requirements file exists
) else (
    echo X Requirements file not found
    goto error_exit
)
echo.

echo 4. Installing dependencies...
pip install pandas openpyxl pillow requests
if %errorlevel% neq 0 (
    echo X Failed to install dependencies
    goto error_exit
) else (
    echo OK Dependencies installed successfully
)
echo.

echo 5. Testing imports...
python -c "import pandas; import openpyxl; import PIL; import requests; print('OK All packages imported successfully')"
if %errorlevel% neq 0 (
    echo X Package import failed
    goto error_exit
)
echo.

echo ========================================
echo OK Environment check completed successfully!
echo ========================================
echo.
echo Now you can:
echo 1. Double-click simple_run.bat to use the tool
echo 2. Or run in command line:
echo    python image_question_generator.py "your_image_folder_path" --no-ai
echo.
pause
exit /b 0

:error_exit
echo.
echo ========================================
echo X Environment check failed
echo ========================================
echo Please resolve the above issues and run this script again
echo.
pause
exit /b 1
