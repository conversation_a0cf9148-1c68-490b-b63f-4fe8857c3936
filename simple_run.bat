@echo off
title Image Question Generator
cls

echo ========================================
echo Vehicle Scene Image Question Generator
echo ========================================
echo.

REM Set example path
set "example_path=D:\test_images"

echo Instructions:
echo 1. Put your images (PNG/JPG format) in a folder
echo 2. Enter the full path to that folder
echo 3. Or drag and drop the folder to this window
echo.
echo Example path format:
echo   %example_path%
echo   C:\Users\<USER>\Desktop\MyImages
echo.

:input_path
set /p image_folder="Enter image folder path: "

REM Remove quotes
set image_folder=%image_folder:"=%

REM Check path
if "%image_folder%"=="" (
    echo Path cannot be empty, please try again
    goto input_path
)

if not exist "%image_folder%" (
    echo Error: Folder does not exist
    echo Path: %image_folder%
    echo Please check if the path is correct
    echo.
    goto input_path
)

echo.
echo Found folder: %image_folder%
echo.

REM Run directly (without AI)
echo Generating questions (no AI mode)...
echo This may take a few minutes, please wait...
echo.

python image_question_generator.py "%image_folder%" --no-ai

if %errorlevel% neq 0 (
    echo.
    echo X Execution failed!
    echo Possible reasons:
    echo 1. Python not installed
    echo 2. Dependencies not installed
    echo 3. Image folder is empty
    echo.
    echo Please run test_setup.bat first to check environment
) else (
    echo.
    echo OK Processing completed!
    echo Please check the generated Excel file in current directory
)

echo.
pause
