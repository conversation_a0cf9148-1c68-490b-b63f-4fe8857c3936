@echo off
chcp 65001 >nul
title 图片问题生成器
cls

echo ========================================
echo 车载场景图片问题自动生成器
echo ========================================
echo.

REM 设置示例路径（用户可以修改）
set "example_path=D:\车载测试图片"

echo 使用说明：
echo 1. 请将你的图片（PNG/JPG格式）放在一个文件夹中
echo 2. 输入该文件夹的完整路径
echo 3. 或者直接拖拽文件夹到此窗口
echo.
echo 示例路径格式：
echo   %example_path%
echo   C:\Users\<USER>\Desktop\我的图片
echo.

:input_path
set /p image_folder="请输入图片文件夹路径: "

REM 去除引号
set image_folder=%image_folder:"=%

REM 检查路径
if "%image_folder%"=="" (
    echo 路径不能为空，请重新输入
    goto input_path
)

if not exist "%image_folder%" (
    echo 错误：文件夹不存在
    echo 路径：%image_folder%
    echo 请检查路径是否正确
    echo.
    goto input_path
)

echo.
echo 找到文件夹：%image_folder%
echo.

REM 直接运行（不使用AI）
echo 正在生成问题（不使用AI模式）...
echo 这可能需要几分钟时间，请耐心等待...
echo.

python image_question_generator.py "%image_folder%" --no-ai

if errorlevel 1 (
    echo.
    echo ❌ 运行失败！
    echo 可能的原因：
    echo 1. Python未安装
    echo 2. 依赖包未安装
    echo 3. 图片文件夹为空
    echo.
    echo 请先运行 test_setup.bat 检查环境
) else (
    echo.
    echo ✅ 处理完成！
    echo 请查看当前目录下生成的Excel文件
)

echo.
pause
