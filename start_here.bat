@echo off
title Vehicle Image Question Generator
cls

echo ========================================
echo Vehicle Scene Image Question Generator
echo ========================================
echo.

echo Instructions:
echo 1. Put your images (PNG/JPG) in a folder
echo 2. Enter the folder path below
echo 3. The tool will generate an Excel file with questions
echo.

:input_path
set /p image_folder="Enter image folder path: "

if "%image_folder%"=="" (
    echo Path cannot be empty!
    goto input_path
)

REM Remove quotes if present
set image_folder=%image_folder:"=%

if not exist "%image_folder%" (
    echo Error: Folder does not exist!
    echo Path: %image_folder%
    echo.
    goto input_path
)

echo.
echo Processing folder: %image_folder%
echo Please wait...
echo.

python simple_generator.py "%image_folder%"

echo.
echo Press any key to exit...
pause >nul
