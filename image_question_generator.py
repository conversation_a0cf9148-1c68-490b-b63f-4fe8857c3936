#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
车载场景图片问题自动生成器
基于需求文档自动生成数据标注问题
"""

import os
import json
import pandas as pd
from pathlib import Path
from typing import List, Dict, Any
import base64
import requests
from PIL import Image
import argparse
from datetime import datetime

class ImageQuestionGenerator:
    def __init__(self, api_key: str = None, model_type: str = "openai"):
        """
        初始化问题生成器
        
        Args:
            api_key: 大模型API密钥
            model_type: 模型类型 ("openai", "claude", "qwen", "wenxin")
        """
        self.api_key = api_key
        self.model_type = model_type
        self.supported_formats = ['.png', '.jpg', '.jpeg']
        
        # 根据需求文档定义的问题模板
        self.question_templates = {
            "人物属性": {
                "属性": [
                    "图片中人物的性别是什么？",
                    "图片中人物的大致年龄段是什么？",
                    "图片中人物的位置在哪里？",
                    "图片中人物的发型是什么样的？",
                    "图片中人物的朝向是什么？",
                    "图片中人物的安全带佩戴情况如何？"
                ],
                "配饰": [
                    "图片中人物是否佩戴帽子？",
                    "图片中人物是否佩戴口罩？",
                    "图片中人物是否佩戴眼镜？",
                    "图片中人物是否佩戴围巾？",
                    "图片中人物是否佩戴耳环？",
                    "图片中人物是否佩戴项链？",
                    "图片中人物是否佩戴耳机？",
                    "图片中人物是否佩戴手套？"
                ],
                "情绪": [
                    "图片中人物的情绪状态是什么？（平静、惊喜、高兴、专注、生气、悲伤、不耐烦、惊讶、焦虑、沮丧、轻松愉悦、欣喜）"
                ],
                "状态": [
                    "图片中人物的状态是什么？（正常、疲劳疲乏、甲醛疲乏、重度疲乏）"
                ],
                "姿态": [
                    "图片中人物的姿态是什么？（坐着、后排横躺、喜优躺、侧身、伸手、背身、前倾、站立、脚放仪表台）"
                ]
            },
            "行为识别": {
                "多人行为": [
                    "图片中是否有交谈行为？",
                    "图片中是否有拥抱行为？",
                    "图片中是否有拥抱行为？",
                    "图片中是否有抱举行为？",
                    "图片中是否有照顾儿童的行为？"
                ],
                "行为": [
                    "图片中人物是否在打电话？",
                    "图片中人物是否在抽烟？",
                    "图片中人物是否在喝水？",
                    "图片中人物是否在睡觉？",
                    "图片中人物是否在化妆？",
                    "图片中人物是否在阅读？",
                    "图片中人物是否在吃东西？",
                    "图片中人物是否在唱歌？",
                    "图片中人物是否在玩游戏？",
                    "图片中人物是否在玩手机？",
                    "图片中人物是否在看手机板？",
                    "图片中人物的头/手是否伸出车窗外？"
                ],
                "儿童关怀": [
                    "图片中是否有哭闹的儿童？",
                    "图片中是否有睡着的儿童？",
                    "图片中儿童是否在准备上下车？"
                ],
                "儿童危险行为": [
                    "图片中儿童是否系安全带？",
                    "图片中儿童的头/手是否伸出车窗外？",
                    "图片中是否有脱高安全座椅的情况？",
                    "图片中是否有摸门锁的情况？",
                    "图片中是否有进入后备箱的情况？",
                    "图片中是否有儿童逗留的情况？"
                ]
            },
            "物体识别": {
                "物体": [
                    "图片中是否有背包？",
                    "图片中是否有手提包？",
                    "图片中是否有手机？",
                    "图片中是否有平板？",
                    "图片中是否有笔记本电脑？",
                    "图片中是否有水杯？",
                    "图片中是否有雨伞？",
                    "图片中是否有毛绒玩具？",
                    "图片中是否有足球？",
                    "图片中是否有篮球？",
                    "图片中是否有网球？",
                    "图片中是否有儿童座椅？",
                    "图片中是否有食物？",
                    "图片中是否有饮料？",
                    "图片中是否有书籍？"
                ],
                "宠物": [
                    "图片中是否有宠物猫？",
                    "图片中是否有宠物狗？",
                    "图片中是否有市侩猫？",
                    "图片中是否有蓝猫？",
                    "图片中是否有英短猫？",
                    "图片中是否有宠物狗（吉娃娃、柯基、金毛、拉布拉多、博美、泰迪）？"
                ]
            },
            "环境识别": {
                "车内环境": [
                    "图片显示的是车内什么区域？（前排、明亮度、整洁度）"
                ],
                "车辆": [
                    "图片中车辆的仪表知识等如何？"
                ]
            },
            "服装识别": {
                "品牌IP": [
                    "图片中是否有可见的物体或衣服的IP或logo？"
                ],
                "服装类型": [
                    "图片中人物的服装类型是什么？（夹克、牛仔外套、风衣、西装、羽绒服、冲锋衣、衬衫、背心、T恤、Polo衫、毛衣、工衣、马甲、棒球服等）"
                ],
                "服装颜色": [
                    "图片中人物服装的主要颜色是什么？（黑、白、红、蓝、灰等基础色系）"
                ]
            }
        }
    
    def get_image_files(self, folder_path: str) -> List[str]:
        """获取文件夹中的所有图片文件"""
        image_files = []
        folder = Path(folder_path)
        
        if not folder.exists():
            raise FileNotFoundError(f"文件夹不存在: {folder_path}")
        
        for file_path in folder.rglob("*"):
            if file_path.suffix.lower() in self.supported_formats:
                image_files.append(str(file_path))
        
        return sorted(image_files)
    
    def encode_image_to_base64(self, image_path: str) -> str:
        """将图片编码为base64"""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    
    def analyze_image_with_ai(self, image_path: str) -> Dict[str, Any]:
        """使用AI分析图片内容"""
        if not self.api_key:
            # 如果没有API密钥，返回模拟数据
            return {
                "has_person": True,
                "person_count": 1,
                "has_child": False,
                "has_pet": False,
                "scene_type": "car_interior",
                "objects_detected": ["phone", "bag"],
                "activities": ["sitting"]
            }

        # 实际的AI分析实现
        base64_image = self.encode_image_to_base64(image_path)

        if self.model_type == "openai":
            return self._analyze_with_openai(base64_image)
        elif self.model_type == "claude":
            return self._analyze_with_claude(base64_image)
        else:
            # 默认返回基础分析
            return self._basic_image_analysis(image_path)

    def _analyze_with_openai(self, base64_image: str) -> Dict[str, Any]:
        """使用OpenAI GPT-4V分析图片"""
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }

        payload = {
            "model": "gpt-4-vision-preview",
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": """请分析这张车载场景图片，返回JSON格式的分析结果，包含以下信息：
                            - has_person: 是否有人物
                            - person_count: 人物数量
                            - has_child: 是否有儿童
                            - has_pet: 是否有宠物
                            - scene_type: 场景类型
                            - objects_detected: 检测到的物体列表
                            - activities: 检测到的行为列表
                            - clothing_colors: 服装颜色
                            - emotions: 情绪状态"""
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}"
                            }
                        }
                    ]
                }
            ],
            "max_tokens": 1000
        }

        try:
            response = requests.post("https://api.openai.com/v1/chat/completions",
                                   headers=headers, json=payload, timeout=30)
            result = response.json()

            if 'choices' in result and len(result['choices']) > 0:
                content = result['choices'][0]['message']['content']
                # 尝试解析JSON
                try:
                    return json.loads(content)
                except:
                    # 如果解析失败，返回基础信息
                    return {"analysis_text": content}

        except Exception as e:
            print(f"API调用失败: {e}")

        return self._basic_image_analysis("")

    def _basic_image_analysis(self, image_path: str) -> Dict[str, Any]:
        """基础图片分析（不使用AI）"""
        return {
            "has_person": True,
            "person_count": 1,
            "has_child": False,
            "has_pet": False,
            "scene_type": "car_interior",
            "objects_detected": [],
            "activities": [],
            "clothing_colors": [],
            "emotions": []
        }

    def generate_questions_for_image(self, image_path: str, analysis_result: Dict[str, Any]) -> List[Dict[str, str]]:
        """根据图片分析结果生成相关问题"""
        questions = []
        image_name = Path(image_path).name

        # 根据分析结果选择相关问题
        if analysis_result.get("has_person", False):
            # 添加人物相关问题
            questions.extend(self._get_person_questions())

        if analysis_result.get("has_child", False):
            # 添加儿童相关问题
            questions.extend(self._get_child_questions())

        if analysis_result.get("has_pet", False):
            # 添加宠物相关问题
            questions.extend(self._get_pet_questions())

        # 添加通用问题
        questions.extend(self._get_general_questions())

        # 为每个问题添加图片信息
        for question in questions:
            question["图片文件"] = image_name
            question["图片路径"] = image_path
            question["生成时间"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        return questions

    def _get_person_questions(self) -> List[Dict[str, str]]:
        """获取人物相关问题"""
        questions = []

        # 人物属性问题
        for category, question_list in self.question_templates["人物属性"].items():
            for question in question_list:
                questions.append({
                    "分类": "人物属性",
                    "子分类": category,
                    "问题": question,
                    "答案": "",
                    "备注": ""
                })

        # 行为识别问题
        for category, question_list in self.question_templates["行为识别"].items():
            if category != "儿童关怀" and category != "儿童危险行为":
                for question in question_list:
                    questions.append({
                        "分类": "行为识别",
                        "子分类": category,
                        "问题": question,
                        "答案": "",
                        "备注": ""
                    })

        # 服装识别问题
        for category, question_list in self.question_templates["服装识别"].items():
            for question in question_list:
                questions.append({
                    "分类": "服装识别",
                    "子分类": category,
                    "问题": question,
                    "答案": "",
                    "备注": ""
                })

        return questions

    def _get_child_questions(self) -> List[Dict[str, str]]:
        """获取儿童相关问题"""
        questions = []

        # 儿童关怀问题
        for question in self.question_templates["行为识别"]["儿童关怀"]:
            questions.append({
                "分类": "行为识别",
                "子分类": "儿童关怀",
                "问题": question,
                "答案": "",
                "备注": ""
            })

        # 儿童危险行为问题
        for question in self.question_templates["行为识别"]["儿童危险行为"]:
            questions.append({
                "分类": "行为识别",
                "子分类": "儿童危险行为",
                "问题": question,
                "答案": "",
                "备注": ""
            })

        return questions

    def _get_pet_questions(self) -> List[Dict[str, str]]:
        """获取宠物相关问题"""
        questions = []

        for question in self.question_templates["物体识别"]["宠物"]:
            questions.append({
                "分类": "物体识别",
                "子分类": "宠物",
                "问题": question,
                "答案": "",
                "备注": ""
            })

        return questions

    def _get_general_questions(self) -> List[Dict[str, str]]:
        """获取通用问题"""
        questions = []

        # 物体识别问题
        for question in self.question_templates["物体识别"]["物体"]:
            questions.append({
                "分类": "物体识别",
                "子分类": "物体",
                "问题": question,
                "答案": "",
                "备注": ""
            })

        # 环境识别问题
        for category, question_list in self.question_templates["环境识别"].items():
            for question in question_list:
                questions.append({
                    "分类": "环境识别",
                    "子分类": category,
                    "问题": question,
                    "答案": "",
                    "备注": ""
                })

        return questions

    def process_images(self, folder_path: str, output_file: str = None, use_ai: bool = True) -> str:
        """处理文件夹中的所有图片并生成问题"""
        print(f"开始处理文件夹: {folder_path}")

        # 获取所有图片文件
        image_files = self.get_image_files(folder_path)
        print(f"找到 {len(image_files)} 个图片文件")

        if not image_files:
            print("未找到支持的图片文件")
            return ""

        all_questions = []

        for i, image_path in enumerate(image_files, 1):
            print(f"处理第 {i}/{len(image_files)} 个图片: {Path(image_path).name}")

            try:
                # 分析图片
                if use_ai and self.api_key:
                    analysis_result = self.analyze_image_with_ai(image_path)
                else:
                    analysis_result = self._basic_image_analysis(image_path)

                # 生成问题
                questions = self.generate_questions_for_image(image_path, analysis_result)
                all_questions.extend(questions)

            except Exception as e:
                print(f"处理图片 {image_path} 时出错: {e}")
                continue

        # 生成输出文件名
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"图片问题生成结果_{timestamp}.xlsx"

        # 保存到Excel
        self.save_to_excel(all_questions, output_file)
        print(f"结果已保存到: {output_file}")

        return output_file

    def save_to_excel(self, questions: List[Dict[str, str]], output_file: str):
        """将问题保存到Excel文件"""
        if not questions:
            print("没有问题需要保存")
            return

        # 创建DataFrame
        df = pd.DataFrame(questions)

        # 重新排列列的顺序
        columns_order = ["图片文件", "图片路径", "分类", "子分类", "问题", "答案", "备注", "生成时间"]
        df = df.reindex(columns=columns_order)

        # 保存到Excel
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 主表
            df.to_excel(writer, sheet_name='问题列表', index=False)

            # 按分类创建不同的工作表
            categories = df['分类'].unique()
            for category in categories:
                category_df = df[df['分类'] == category]
                sheet_name = category.replace('/', '_')  # Excel工作表名不能包含某些字符
                category_df.to_excel(writer, sheet_name=sheet_name, index=False)

        print(f"共生成 {len(questions)} 个问题")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='车载场景图片问题自动生成器')
    parser.add_argument('folder_path', help='图片文件夹路径')
    parser.add_argument('-o', '--output', help='输出Excel文件路径')
    parser.add_argument('-k', '--api-key', help='大模型API密钥')
    parser.add_argument('-m', '--model', choices=['openai', 'claude', 'qwen', 'wenxin'],
                       default='openai', help='使用的大模型类型')
    parser.add_argument('--no-ai', action='store_true', help='不使用AI分析，生成所有问题')

    args = parser.parse_args()

    # 创建问题生成器
    generator = ImageQuestionGenerator(api_key=args.api_key, model_type=args.model)

    # 处理图片
    try:
        output_file = generator.process_images(
            folder_path=args.folder_path,
            output_file=args.output,
            use_ai=not args.no_ai
        )
        print(f"\n✅ 处理完成！结果文件: {output_file}")

    except Exception as e:
        print(f"❌ 处理失败: {e}")


if __name__ == "__main__":
    main()
