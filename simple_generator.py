#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版车载场景图片问题生成器
"""

import os
import pandas as pd
from pathlib import Path
from datetime import datetime
import sys

def get_image_files(folder_path):
    """获取文件夹中的所有图片文件"""
    supported_formats = ['.png', '.jpg', '.jpeg']
    image_files = []
    
    folder = Path(folder_path)
    if not folder.exists():
        print(f"错误：文件夹不存在 - {folder_path}")
        return []
    
    for file_path in folder.rglob("*"):
        if file_path.suffix.lower() in supported_formats:
            image_files.append(str(file_path))
    
    return sorted(image_files)

def generate_questions():
    """生成所有问题模板"""
    questions = []
    
    # 人物属性问题
    person_questions = [
        "图片中人物的性别是什么？",
        "图片中人物的大致年龄段是什么？",
        "图片中人物的位置在哪里？",
        "图片中人物的发型是什么样的？",
        "图片中人物的朝向是什么？",
        "图片中人物的安全带佩戴情况如何？",
        "图片中人物是否佩戴帽子？",
        "图片中人物是否佩戴口罩？",
        "图片中人物是否佩戴眼镜？",
        "图片中人物是否佩戴围巾？",
        "图片中人物是否佩戴耳环？",
        "图片中人物是否佩戴项链？",
        "图片中人物是否佩戴耳机？",
        "图片中人物是否佩戴手套？",
        "图片中人物的情绪状态是什么？（平静、惊喜、高兴、专注、生气、悲伤、不耐烦、惊讶、焦虑、沮丧、轻松愉悦、欣喜）",
        "图片中人物的状态是什么？（正常、疲劳疲乏、甲醛疲乏、重度疲乏）",
        "图片中人物的姿态是什么？（坐着、后排横躺、喜优躺、侧身、伸手、背身、前倾、站立、脚放仪表台）"
    ]
    
    for q in person_questions:
        questions.append({
            "分类": "人物属性",
            "子分类": "基础属性",
            "问题": q,
            "答案": "",
            "备注": ""
        })
    
    # 行为识别问题
    behavior_questions = [
        "图片中是否有交谈行为？",
        "图片中是否有拥抱行为？",
        "图片中是否有抱举行为？",
        "图片中是否有照顾儿童的行为？",
        "图片中人物是否在打电话？",
        "图片中人物是否在抽烟？",
        "图片中人物是否在喝水？",
        "图片中人物是否在睡觉？",
        "图片中人物是否在化妆？",
        "图片中人物是否在阅读？",
        "图片中人物是否在吃东西？",
        "图片中人物是否在唱歌？",
        "图片中人物是否在玩游戏？",
        "图片中人物是否在玩手机？",
        "图片中人物是否在看手机板？",
        "图片中人物的头/手是否伸出车窗外？",
        "图片中是否有哭闹的儿童？",
        "图片中是否有睡着的儿童？",
        "图片中儿童是否在准备上下车？",
        "图片中儿童是否系安全带？",
        "图片中儿童的头/手是否伸出车窗外？",
        "图片中是否有脱高安全座椅的情况？",
        "图片中是否有摸门锁的情况？",
        "图片中是否有进入后备箱的情况？",
        "图片中是否有儿童逗留的情况？"
    ]
    
    for q in behavior_questions:
        questions.append({
            "分类": "行为识别",
            "子分类": "行为分析",
            "问题": q,
            "答案": "",
            "备注": ""
        })
    
    # 物体识别问题
    object_questions = [
        "图片中是否有背包？",
        "图片中是否有手提包？",
        "图片中是否有手机？",
        "图片中是否有平板？",
        "图片中是否有笔记本电脑？",
        "图片中是否有水杯？",
        "图片中是否有雨伞？",
        "图片中是否有毛绒玩具？",
        "图片中是否有足球？",
        "图片中是否有篮球？",
        "图片中是否有网球？",
        "图片中是否有儿童座椅？",
        "图片中是否有食物？",
        "图片中是否有饮料？",
        "图片中是否有书籍？",
        "图片中是否有宠物猫？",
        "图片中是否有宠物狗？"
    ]
    
    for q in object_questions:
        questions.append({
            "分类": "物体识别",
            "子分类": "物体检测",
            "问题": q,
            "答案": "",
            "备注": ""
        })
    
    # 服装识别问题
    clothing_questions = [
        "图片中是否有可见的物体或衣服的IP或logo？",
        "图片中人物的服装类型是什么？（夹克、牛仔外套、风衣、西装、羽绒服、冲锋衣、衬衫、背心、T恤、Polo衫、毛衣、工衣、马甲、棒球服等）",
        "图片中人物服装的主要颜色是什么？（黑、白、红、蓝、灰等基础色系）"
    ]
    
    for q in clothing_questions:
        questions.append({
            "分类": "服装识别",
            "子分类": "服装分析",
            "问题": q,
            "答案": "",
            "备注": ""
        })
    
    # 环境识别问题
    environment_questions = [
        "图片显示的是车内什么区域？（前排、明亮度、整洁度）",
        "图片中车辆的仪表知识等如何？"
    ]
    
    for q in environment_questions:
        questions.append({
            "分类": "环境识别",
            "子分类": "环境分析",
            "问题": q,
            "答案": "",
            "备注": ""
        })
    
    return questions

def process_images(folder_path, output_file=None):
    """处理图片文件夹并生成问题"""
    print(f"开始处理文件夹: {folder_path}")
    
    # 获取图片文件
    image_files = get_image_files(folder_path)
    print(f"找到 {len(image_files)} 个图片文件")
    
    if not image_files:
        print("未找到支持的图片文件（PNG/JPG格式）")
        return
    
    # 生成问题模板
    question_template = generate_questions()
    print(f"生成了 {len(question_template)} 个问题模板")
    
    # 为每张图片生成问题
    all_questions = []
    for i, image_path in enumerate(image_files, 1):
        print(f"处理第 {i}/{len(image_files)} 个图片: {Path(image_path).name}")
        
        for question in question_template:
            new_question = question.copy()
            new_question["图片文件"] = Path(image_path).name
            new_question["图片路径"] = image_path
            new_question["生成时间"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            all_questions.append(new_question)
    
    # 生成输出文件名
    if not output_file:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"图片问题生成结果_{timestamp}.xlsx"
    
    # 保存到Excel
    print(f"正在保存到Excel文件: {output_file}")
    df = pd.DataFrame(all_questions)
    
    # 重新排列列的顺序
    columns_order = ["图片文件", "图片路径", "分类", "子分类", "问题", "答案", "备注", "生成时间"]
    df = df.reindex(columns=columns_order)
    
    # 保存Excel文件，尝试不同的引擎
    try:
        # 首先尝试使用xlsxwriter
        with pd.ExcelWriter(output_file, engine='xlsxwriter') as writer:
            # 主表
            df.to_excel(writer, sheet_name='问题列表', index=False)

            # 按分类创建不同的工作表
            categories = df['分类'].unique()
            for category in categories:
                category_df = df[df['分类'] == category]
                sheet_name = category.replace('/', '_')
                category_df.to_excel(writer, sheet_name=sheet_name, index=False)
    except Exception as e:
        print(f"xlsxwriter引擎失败，尝试openpyxl: {e}")
        try:
            # 备选：使用openpyxl
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                # 主表
                df.to_excel(writer, sheet_name='问题列表', index=False)

                # 按分类创建不同的工作表
                categories = df['分类'].unique()
                for category in categories:
                    category_df = df[df['分类'] == category]
                    sheet_name = category.replace('/', '_')
                    category_df.to_excel(writer, sheet_name=sheet_name, index=False)
        except Exception as e2:
            print(f"openpyxl引擎也失败，保存为CSV: {e2}")
            # 最后备选：保存为CSV
            csv_file = output_file.replace('.xlsx', '.csv')
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            print(f"已保存为CSV文件: {csv_file}")
            return csv_file
    
    print(f"✅ 处理完成！")
    print(f"📊 共生成 {len(all_questions)} 个问题")
    print(f"📁 结果文件: {output_file}")
    return output_file

def main():
    """主函数"""
    print("========================================")
    print("车载场景图片问题自动生成器（简化版）")
    print("========================================")
    print()
    
    if len(sys.argv) < 2:
        # 交互式输入
        folder_path = input("请输入图片文件夹路径: ").strip().strip('"')
    else:
        # 命令行参数
        folder_path = sys.argv[1]
    
    if not folder_path:
        print("错误：未提供文件夹路径")
        return
    
    try:
        output_file = process_images(folder_path)
        print(f"\n🎉 成功！请查看生成的Excel文件: {output_file}")
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
