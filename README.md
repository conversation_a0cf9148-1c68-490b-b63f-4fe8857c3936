# 车载场景图片问题自动生成器

这是一个专为车载工程师和大模型测试工程师设计的工具，能够根据车载场景图片自动生成数据标注问题。

## 功能特点

- 🚗 **专业车载场景**: 基于车载工程需求文档设计的问题模板
- 🤖 **AI智能分析**: 支持多种大模型API（OpenAI GPT-4V、Claude等）
- 📊 **Excel输出**: 自动生成结构化的Excel问题表格
- 🎯 **智能筛选**: 根据图片内容智能选择相关问题
- 📁 **批量处理**: 支持文件夹批量处理图片

## 支持的问题类型

根据需求文档，工具支持以下类型的问题生成：

### 1. 人物属性
- **属性**: 性别、年龄、位置、发型、朝向、安全带
- **配饰**: 帽子、口罩、眼镜、围巾、耳环、项链、耳机、手套
- **情绪**: 平静、惊喜、高兴、专注、生气、悲伤等
- **状态**: 正常、疲劳疲乏、甲醛疲乏、重度疲乏
- **姿态**: 坐着、后排横躺、侧身、伸手、背身等

### 2. 行为识别
- **多人行为**: 交谈、拥抱、抱举、照顾儿童
- **个人行为**: 打电话、抽烟、喝水、睡觉、化妆、阅读等
- **儿童关怀**: 哭闹、睡着、准备上下车
- **儿童危险行为**: 安全带、伸出车窗、脱离座椅等

### 3. 物体识别
- **常见物体**: 背包、手机、平板、水杯、食物等
- **宠物识别**: 猫、狗及各种品种

### 4. 环境识别
- **车内环境**: 前排、明亮度、整洁度
- **车辆信息**: 仪表知识等

### 5. 服装识别
- **品牌IP**: 可见的IP或logo
- **服装类型**: 夹克、风衣、T恤、Polo衫等
- **服装颜色**: 基础色系识别

## 安装依赖

```bash
pip install pandas openpyxl pillow requests
```

## 使用方法

### 1. 基础使用（不使用AI）

```bash
python image_question_generator.py /path/to/your/images --no-ai
```

这将为所有图片生成完整的问题集合，适合手动标注。

### 2. 使用OpenAI GPT-4V

```bash
python image_question_generator.py /path/to/your/images -k your_openai_api_key -m openai
```

### 3. 指定输出文件

```bash
python image_question_generator.py /path/to/your/images -o 我的标注问题.xlsx --no-ai
```

### 4. 完整参数示例

```bash
python image_question_generator.py /path/to/your/images \
    --api-key sk-your-api-key \
    --model openai \
    --output 车载场景标注_20240801.xlsx
```

## 参数说明

- `folder_path`: 图片文件夹路径（必需）
- `-o, --output`: 输出Excel文件路径
- `-k, --api-key`: 大模型API密钥
- `-m, --model`: 模型类型（openai/claude/qwen/wenxin）
- `--no-ai`: 不使用AI分析，生成所有问题

## 输出格式

生成的Excel文件包含以下列：

- **图片文件**: 图片文件名
- **图片路径**: 完整路径
- **分类**: 问题大分类（人物属性、行为识别等）
- **子分类**: 问题子分类（属性、配饰等）
- **问题**: 具体问题内容
- **答案**: 空白，供手动填写
- **备注**: 空白，供添加备注
- **生成时间**: 问题生成时间

## 配置文件

可以通过修改 `config.json` 文件来自定义设置：

- API配置
- 图片处理设置
- 输出格式设置
- 问题生成设置

## 注意事项

1. **API密钥安全**: 请妥善保管你的API密钥，不要提交到版本控制系统
2. **图片格式**: 目前支持PNG、JPG格式
3. **文件大小**: 建议单个图片不超过10MB
4. **网络连接**: 使用AI功能时需要稳定的网络连接

## 示例输出

```
开始处理文件夹: /path/to/images
找到 15 个图片文件
处理第 1/15 个图片: car_interior_001.jpg
处理第 2/15 个图片: dashboard_view.png
...
共生成 750 个问题
结果已保存到: 图片问题生成结果_20240801_143022.xlsx
✅ 处理完成！
```

## 技术支持

如有问题或建议，请联系开发团队。

---

**开发者**: 车载工程师 & 大模型测试工程师  
**版本**: 1.0.0  
**更新日期**: 2024-08-01
